import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;
let createdOwnerId: string;

describe(')))))))))))))) Opportunity Owner Status Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  it('search opp owners by emailAddress', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          queryOpportunityOwners(
            searchSortInput: {
              searchFields: [
                {
                  fieldNames: ["owner.user.emailAddress"]
                  operator: MATCH
                  searchValue: "<EMAIL>"
                }
              ]
            }
          ) {      
            results {  
                id createdAt updatedAt status isRemoved
                owner {
                    organizationRole
                    user {
                        emailAddress firstName lastName org1
                    }
                }
            }
          }
        }`,
      })
      .expect(200);
    const { results } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(12);
    expect(results[0].owner.user.emailAddress).to.equal('<EMAIL>');
  });

  it('filter owner by email using IN', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunityOwners(
          searchSortInput: {
            searchFields: [
                {
                  fieldNames: ["owner.user.emailAddress"]
                  operator: IN
                  searchValue: ["<EMAIL>", "<EMAIL>"]
                }
            ]
          }
        ) {
          results {  
            id createdAt updatedAt status isRemoved
            owner {
                organizationRole
                user {
                    emailAddress firstName lastName org1
                }
            }
          }
        }
      }
      `,
      })
      .expect(200);
    const { results } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(20);
  });

  it('filter owners using OR and AND by using both searchFields and jsonSearchGroups', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunityOwners(
          searchSortInput: {
            searchFields: [{ fieldNames: ["owner.organizationRole"], operator: MATCH, searchValue: ["my random role"] }],
            jsonSearchGroups: [
              { operator: "or", operands: [
                { fieldNames: ["owner.user.emailAddress"], operator: "~", searchValue: "curator" },
                { fieldNames: ["owner.user.emailAddress"], operator: "~", searchValue: "admin" }
              ] }
            ]
            sortFields: [{ fieldName: "owner.user.emailAddress", ascending: true }]
          }
        ) {
          results {  
              id createdAt updatedAt status isRemoved
                owner {
                    organizationRole
                    user {
                        emailAddress firstName lastName org1
                    }
                }
            }
        }
      }
      `,
      })
      .expect(200);
    const { results } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(14);
    expect(results[0].owner.user.emailAddress).to.equal('<EMAIL>');
    expect(results[9].owner.user.emailAddress).to.equal('<EMAIL>');
  });

  it('page owners and sort by id and owner first name', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunityOwners(
          pagingInput: { pageSize: 5, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: false }, { fieldName: "owner.user.firstName", ascending: true}]
          }
        ) {
          results {  
              id createdAt updatedAt status isRemoved
                owner {
                    organizationRole
                    user {
                        emailAddress firstName lastName org1
                    }
                }
            }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(5);
    expect(pageInfo.hasNext).equal(true);
    expect(pageInfo.hasPrevious).equal(false);
    expect(pageInfo.lastCursor).equal('0');
    expect(pageInfo.lastPageSize).equal(5);
    expect(pageInfo.retrievedCount).equal(5);
    expect(pageInfo.totalCount).equal(40);
  });

  it('page owners and sort by last name first name', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOpportunityOwners(
          pagingInput: { pageSize: 25, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "owner.user.lastName", ascending: false }, { fieldName: "owner.user.firstName", ascending: false }]
          }
        ) {
          results {  
              id createdAt updatedAt status isRemoved
                owner {
                    organizationRole
                    user {
                        emailAddress firstName lastName org1
                    }
                }
            }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(25);
    expect(pageInfo.hasNext).equal(true);
    expect(pageInfo.hasPrevious).equal(false);
    expect(pageInfo.lastCursor).equal('0');
    expect(pageInfo.lastPageSize).equal(25);
    expect(pageInfo.retrievedCount).equal(25);
    expect(pageInfo.totalCount).equal(40);
  });

  it('page forward opportunities (no more records)', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
            queryOpportunityOwners(
                pagingInput: { pageSize: 25, cursor: "110" },
                searchSortInput: {
                    sortFields: [{ fieldName: "owner.user.lastName", ascending: false }, { fieldName: "owner.user.firstName", ascending: false }]
                }
            ) {
                results {  
                    id createdAt updatedAt status isRemoved
                        owner {
                            organizationRole
                            user {
                                emailAddress firstName lastName org1
                            }
                        }
                    }
                pageInfo {
                    hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
                }
            }
        }
        `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(0);
    expect(pageInfo.hasNext).equal(false);
    expect(pageInfo.hasPrevious).equal(true);
    expect(pageInfo.lastCursor).equal('110');
    expect(pageInfo.lastPageSize).equal(25);
    expect(pageInfo.retrievedCount).equal(0);
    expect(pageInfo.totalCount).equal(40);
  });

  it('page opportunities with offset', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
            queryOpportunityOwners(
                pagingInput: { pageSize: 3, cursor: "9" },
                searchSortInput: {
                    sortFields: [{ fieldName: "id", ascending: true }]
                }
            ) {
                results {  
                    id createdAt updatedAt status isRemoved
                    owner {
                        organizationRole
                        user {
                            emailAddress firstName lastName org1
                        }
                    }
                }
                pageInfo {
                    hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
                }
            }
        }
        `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(3);
    expect(pageInfo.hasNext).equal(true);
    expect(pageInfo.hasPrevious).equal(true);
    expect(pageInfo.lastCursor).equal('9');
    expect(pageInfo.lastPageSize).equal(3);
    expect(pageInfo.retrievedCount).equal(3);
    expect(pageInfo.totalCount).equal(40);
    expect(results[0].id).equal(Id.simpleId(12, ID_PREFIX_0));
    expect(results[2].id).equal(Id.simpleId(26, ID_PREFIX_0));
  });

  it('page opportunities with offset larger than available results', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
            queryOpportunityOwners(
                pagingInput: { pageSize: 5, cursor: "114" },
                searchSortInput: {
                    sortFields: [{ fieldName: "id", ascending: true } ]
                }
            ) {
                results {  
                    id createdAt updatedAt status isRemoved
                    owner {
                        organizationRole
                        user {
                            emailAddress firstName lastName org1
                        }
                    }
                }
                pageInfo {
                    hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
                }
            }
        }
        `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOpportunityOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(0);
    expect(pageInfo.hasNext).equal(false);
    expect(pageInfo.hasPrevious).equal(true);
    expect(pageInfo.lastCursor).equal('114');
    expect(pageInfo.lastPageSize).equal(5);
    expect(pageInfo.retrievedCount).equal(0);
    expect(pageInfo.totalCount).equal(40);
  });

  it('should create owner for another user', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createOpportunityOwner(
            input: {
              emailAddress: "<EMAIL>"
              firstName: "Johnny"
              lastName: "Smith"
              org1: "Something"
              org2: "NA"
              org3: "DA"
              org4: "TA"
              phone: "3242342342"
              altContact: "<EMAIL>"
              organizationRole: "Tada"
              opportunityId: "00000000-0000-0000-0000-000000000001"
            }
          ) {
            id createdAt updatedAt status isRemoved
            owner {
                organizationRole
                user {
                    emailAddress firstName lastName org1
                }
            }
          }
        }
        `,
      })
      .expect(200);

    const { createOpportunityOwner } = response.body.data;
    expect(createOpportunityOwner).to.be.a('object');
    expect(createOpportunityOwner.id).to.not.be.null;
    expect(createOpportunityOwner.owner.user.emailAddress).to.not.be.null;
    createdOwnerId = createOpportunityOwner.id;
  });

  it('should create owner for existing user', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createOpportunityOwner(
            input: {
              emailAddress: "<EMAIL>"
              firstName: "Curator"
              lastName: "User"
              org1: "Something"
              org2: "NA"
              org3: "DA"
              org4: "TA"
              phone: "3242342342"
              altContact: "<EMAIL>"
              organizationRole: "Tada"
              opportunityId: "00000000-0000-0000-0000-000000000001"
            }
          ) {
            id createdAt updatedAt status isRemoved
            owner {
                organizationRole
                user {
                    emailAddress firstName lastName org1
                }
            }
          }
        }
        `,
      })
      .expect(200);

    const { createOpportunityOwner } = response.body.data;
    expect(createOpportunityOwner).to.be.a('object');
    expect(createOpportunityOwner.id).to.not.be.null;
    expect(createOpportunityOwner.owner.user.emailAddress).to.not.be.null;
  });

  it('should create owner for existing user', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createOpportunityOwner(
            input: {
              emailAddress: "<EMAIL>"
              firstName: "Curator"
              lastName: "User"
              org1: "Something"
              org2: "NA"
              org3: "DA"
              org4: "TA"
              phone: "3242342342"
              altContact: "<EMAIL>"
              organizationRole: "Tada"
              opportunityId: "00000000-0000-0000-0000-000000000001"
            }
          ) {
            id createdAt updatedAt status isRemoved
            owner {
                organizationRole
                user {
                    emailAddress firstName lastName org1
                }
            }
          }
        }
        `,
      })
      .expect(200);

    const { createOpportunityOwner } = response.body.data;
    expect(createOpportunityOwner).to.be.a('object');
    expect(createOpportunityOwner.id).to.not.be.null;
    expect(createOpportunityOwner.owner.user.emailAddress).to.not.be.null;
  });

  it('should update owner status and set dates', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOpportunityOwner(
          id: "${createdOwnerId}"
          input: { organizationRole: "Updated my role", status: REMOVED }
          links: {}
        ) {
            id createdAt updatedAt status isRemoved
            owner {
                organizationRole
            }
        }
      }
      `,
      })
      .expect(200);

    const { updateOpportunityOwner } = response.body.data;
    expect(updateOpportunityOwner).to.be.a('object');
    expect(updateOpportunityOwner.id).to.not.be.null;
    expect(updateOpportunityOwner.owner.id).to.not.be.null;
    expect(updateOpportunityOwner.owner.organizationRole).to.equal('Updated my role');
  });

  describe('Initial Owner Creation Logic', () => {
    it('should create initial owner when none exists and submission has user', async () => {
      restoreDbFromLocalBackup();
      console.log('🚀 Database cleared, fixtures loaded');

      // First, create a submission for an opportunity that doesn't have an initial owner
      const submissionResponse = await request
        .post('/user')
        .send({
          query: `mutation {
            submitSubmission(
              input: {
                title: "Test Submission for Initial Owner"
                statement: "This is a test submission"
                context: "Testing initial owner creation"
              }
            ) {
              id
              title
              opportunities {
                id
              }
            }
          }`,
        })
        .expect(200);

      const submission = submissionResponse.body.data.submitSubmission;
      expect(submission.opportunities).to.be.an('array');
      expect(submission.opportunities.length).to.be.greaterThan(0);

      const opportunityId = submission.opportunities[0].id;

      // Verify no initial owner exists for this opportunity
      const initialCheckResponse = await request
        .post('/curator')
        .set('Authorization', `Bearer ${curatorToken}`)
        .send({
          query: `query {
            queryOpportunityOwners(
              searchSortInput: {
                searchFields: [
                  {
                    fieldNames: ["opportunity.id"]
                    operator: EQ
                    searchValue: "${opportunityId}"
                  },
                  {
                    fieldNames: ["status"]
                    operator: EQ
                    searchValue: "INITIAL"
                  }
                ]
              }
            ) {
              results {
                id
                status
                opportunity {
                  id
                }
              }
            }
          }`,
        })
        .expect(200);

      expect(initialCheckResponse.body.data.queryOpportunityOwners.results).to.have.length(0);

      // Now create an opportunity owner, which should trigger initial owner creation
      const createOwnerResponse = await request
        .post('/curator')
        .set('Authorization', `Bearer ${curatorToken}`)
        .send({
          query: `mutation {
            createOpportunityOwner(
              input: {
                emailAddress: "<EMAIL>"
                firstName: "New"
                lastName: "Owner"
                opportunityId: "${opportunityId}"
              }
            ) {
              id
              status
              owner {
                user {
                  emailAddress
                }
              }
            }
          }`,
        })
        .expect(200);

      expect(createOwnerResponse.body.data.createOpportunityOwner).to.be.an('object');

      // Verify that an initial owner was created
      const finalCheckResponse = await request
        .post('/curator')
        .set('Authorization', `Bearer ${curatorToken}`)
        .send({
          query: `query {
            queryOpportunityOwners(
              searchSortInput: {
                searchFields: [
                  {
                    fieldNames: ["opportunity.id"]
                    operator: EQ
                    searchValue: "${opportunityId}"
                  },
                  {
                    fieldNames: ["status"]
                    operator: EQ
                    searchValue: "INITIAL"
                  }
                ]
              }
            ) {
              results {
                id
                status
                createdAt
                opportunity {
                  id
                }
                owner {
                  user {
                    emailAddress
                  }
                }
              }
            }
          }`,
        })
        .expect(200);

      const initialOwners = finalCheckResponse.body.data.queryOpportunityOwners.results;
      expect(initialOwners).to.have.length(1);
      expect(initialOwners[0].status).to.equal('INITIAL');
      expect(initialOwners[0].opportunity.id).to.equal(opportunityId);
      expect(initialOwners[0].owner.user).to.not.be.null;
    });

    it('should not create duplicate initial owner when one already exists', async () => {
      restoreDbFromLocalBackup();
      console.log('🚀 Database cleared, fixtures loaded');

      // Use an existing opportunity that should already have an initial owner
      const existingOpportunityId = '00000000-0000-0000-0000-000000000001';

      // Check initial state - should have an initial owner
      const initialCheckResponse = await request
        .post('/curator')
        .set('Authorization', `Bearer ${curatorToken}`)
        .send({
          query: `query {
            queryOpportunityOwners(
              searchSortInput: {
                searchFields: [
                  {
                    fieldNames: ["opportunity.id"]
                    operator: EQ
                    searchValue: "${existingOpportunityId}"
                  },
                  {
                    fieldNames: ["status"]
                    operator: EQ
                    searchValue: "INITIAL"
                  }
                ]
              }
            ) {
              results {
                id
                status
              }
            }
          }`,
        })
        .expect(200);

      const initialOwnersCount = initialCheckResponse.body.data.queryOpportunityOwners.results.length;

      // Create a new opportunity owner for the same opportunity
      const createOwnerResponse = await request
        .post('/curator')
        .set('Authorization', `Bearer ${curatorToken}`)
        .send({
          query: `mutation {
            createOpportunityOwner(
              input: {
                emailAddress: "<EMAIL>"
                firstName: "Another"
                lastName: "Owner"
                opportunityId: "${existingOpportunityId}"
              }
            ) {
              id
              status
            }
          }`,
        })
        .expect(200);

      expect(createOwnerResponse.body.data.createOpportunityOwner).to.be.an('object');

      // Verify that no additional initial owner was created
      const finalCheckResponse = await request
        .post('/curator')
        .set('Authorization', `Bearer ${curatorToken}`)
        .send({
          query: `query {
            queryOpportunityOwners(
              searchSortInput: {
                searchFields: [
                  {
                    fieldNames: ["opportunity.id"]
                    operator: EQ
                    searchValue: "${existingOpportunityId}"
                  },
                  {
                    fieldNames: ["status"]
                    operator: EQ
                    searchValue: "INITIAL"
                  }
                ]
              }
            ) {
              results {
                id
                status
              }
            }
          }`,
        })
        .expect(200);

      const finalOwnersCount = finalCheckResponse.body.data.queryOpportunityOwners.results.length;
      expect(finalOwnersCount).to.equal(initialOwnersCount); // Should not have increased
    });
  });
});

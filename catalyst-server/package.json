{"name": "catalyst-server", "description": "The Catalyst Server", "version": "2.0.3", "license": "MIT", "author": "<PERSON>", "main": "src/index.ts", "private": true, "scripts": {"prepare": "cd .. && husky install catalyst-server/.husky", "compile": "tsc -p ./tsconfig.prod.json", "build": "npm-run-all clean-dist compile write-version-file", "tsoa": "tsoa spec-and-routes", "commit-build": "npm-run-all clean-dist compile", "release": "commit-and-tag-version", "dev": "cross-env NODE_DEV=true ts-node-dev --preserve-symlinks -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/index.ts", "dev-api": "cross-env NODE_DEV=true ts-node-dev --preserve-symlinks -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/api/index.ts", "gen-env-types": "gen-env-types .env -o src/core/utils/types/env.d.ts -e .", "start": "cross-env NODE_PATH=./dist node dist/index.js", "start-api": "cross-env NODE_PATH=./dist node dist/api/index.js", "lint": "eslint src/**/*.ts --fix", "test": "cross-env NODE_DEV=true NODE_ENV=test NODE_PRESERVE_SYMLINKS=1 ./node_modules/.bin/mocha --config ./.mocharc.json", "test:coverage": "npm run test -- --reporter mocha-junit-reporter --reporter-option 'mochaFile=./coverage/junit-reporter.xml'", "load": "cross-env NODE_DEV=true ts-node-dev --preserve-symlinks -r tsconfig-paths/register -r reflect-metadata -r dotenv/config tests/performance/loadTest.ts", "bootstrap-db": "cross-env NODE_DEV=true ts-node-dev --preserve-symlinks -r tsconfig-paths/register -r reflect-metadata -r dotenv/config tests/bootstrapDb.ts", "dbCli": "cross-env NODE_DEV=true ts-node-dev --preserve-symlinks -r tsconfig-paths/register -r reflect-metadata -r dotenv/config src/core/utils/dbCli.ts", "clean-dist": "<PERSON><PERSON><PERSON> dist", "prod-env": "cp .env-prod .env", "dev-env": "cp .env-dev .env", "test-env": "cp .env-test .env", "staging-env": "cp .env-staging .env", "corp-env": "cp .env-azurecorp .env", "copy-web-build": "mkdir -p ./dist/web/ic && cp -r ../catalyst-innovation-client/web-build/* ./dist/web/ && cp -r ../catalyst-curator-client/web-build/* ./dist/web/ic/", "write-version-file": "echo \"{ \\\"serverVersion\\\": \\\"$npm_package_version\\\" }\" | tee dist/version.json"}, "dependencies": {"@azure/storage-blob": "^12.8.0", "@mikro-orm/core": "^6.4.14", "@mikro-orm/migrations": "^6.4.14", "@mikro-orm/postgresql": "^6.4.14", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.3.0", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-graphql": "^0.12.0", "express-jwt": "^8.4.1", "fast-csv": "^4.3.6", "gen-env-types": "^1.3.0", "graphql": "^15.8.0", "graphql-fields": "^2.0.3", "graphql-playground-middleware-express": "^1.7.23", "graphql-upload": "^13.0.0", "jsonwebtoken": "^8.5.1", "reflect-metadata": "^0.1.13", "swagger-ui-express": "^5.0.0", "tsoa": "^6.2.0", "type-graphql": "^1.1.1", "uuid": "^8.3.2"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/bcrypt": "^5.0.0", "@types/chai": "^4.2.21", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/faker": "5.5.7", "@types/graphql-fields": "^1.3.5", "@types/graphql-upload": "^8.0.12", "@types/jsonwebtoken": "^8.5.5", "@types/mocha": "^9.0.0", "@types/supertest": "^2.0.11", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^8.3.1", "@types/validator": "^13.7.1", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "axios": "^1.6.8", "chai": "^4.3.4", "commit-and-tag-version": "^12.4.1", "cross-env": "^7.0.3", "eslint": "^8.9.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^5.1.3", "faker": "^5.5.3", "husky": "^9.0.11", "mocha": "^9.0.3", "mocha-junit-reporter": "^2.2.1", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "prettier": "^3.2.5", "rimraf": "^3.0.2", "supertest": "^6.1.4", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "mikro-orm": {"useTsNode": true, "configPaths": ["./src/mikro-orm.config.ts"]}, "engines": {"node": ">=14.0.0"}}